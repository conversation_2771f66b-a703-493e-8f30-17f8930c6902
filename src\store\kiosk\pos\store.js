import { create } from "zustand";
import { createTrackedSelector } from "react-tracked";
import CommonHelper from "@/utils/CommonHelper";
import ApiHelper from "@/utils/ApiHelper";
import Constants from "@/utils/Constants";
import Router from "next/router";
import { useCheckInStore } from "./modal/storeCheckIn";
import { usePersistKioskPosStore } from "./storePersist";

const page = (set, get) => ({
  setState: (newValue, cb = null, keys = []) => {
    set((prevState) => {
      if (keys.length > 0) {
        return CommonHelper.updateNestedState(prevState, keys, newValue);
      }

      // Shallow update if no keys provided
      return {
        ...prevState,
        ...newValue,
      };
    });

    if (cb) {
      setTimeout(() => {
        cb();
      }, 0);
    }
  },
  selected_aol: null,
  selected_branch: null,

  ref_Loading: null,
  ref_MySnackbar: null,
  isReady: false,
  arrCategory: [],
  selectedCategory: null,

  pageProduct: 1,
  limitProduct: 10,
  totalPageProduct: 1,

  isGetProduct: false,
  isLoadMoreProduct: false,
  arrProducts: [],
  totalProduct: 0,
  searchProduct: "",
  sortProduct: "placeholder",
  searchProductDebounceTimer: null,

  filterProductSellingPriceRange: [0, 0],
  filterProductSellingPriceMaxRange: 0,

  onLoading: (show, timeout = 300) => {
    if (get().ref_Loading.current) {
      if (show) {
        get().ref_Loading.current.onShowDialog();
      } else {
        setTimeout(() => {
          get().ref_Loading.current.onCloseDialog();
        }, timeout);
      }
    }
  },
  onNotify: (message, severity) => {
    if (get().ref_MySnackbar.current?.onNotify) {
      get().ref_MySnackbar.current?.onNotify(message, severity);
      return;
    }
  },

  onLogoutListeners: async () => {
    get().onLoading(true);
    let response = await ApiHelper.post("kiosk/kiosk/auth/logout", {});
    get().onLoading(false, 0);
    Router.replace(Constants.webUrl.login);
  },

  onInit: async () => {
    set({ isReady: false });
    get().onLoading(true);

    await get().onFetchSummary(null);

    await Promise.all([
      CommonHelper.task("onFetchCategory", async (resolve) => {
        get().onFetchCategory(resolve);
      }),
      CommonHelper.task("onFetchProduct", async (resolve) => {
        get().onRefreshProduct(resolve);
      }),
    ]);

    get().onLoading(false, 0);
    // set({ isReady: true });
  },

  onReset: () => {
    if (get().searchProductDebounceTimer) {
      clearTimeout(get().searchProductDebounceTimer);
    }

    get().onResetCategory();

    set({
      pageProduct: 1,
      limitProduct: 10,
      totalPageProduct: 1,

      isGetProduct: false,
      isLoadMoreProduct: false,
      arrProducts: [],
      totalProduct: 0,
      searchProduct: "",
      sortProduct: "placeholder",
      searchProductDebounceTimer: null,

      filterProductSellingPriceRange: [
        0,
        get().filterProductSellingPriceMaxRange,
      ],
    });

    get().onRefreshProduct(null);
  },

  onResetCategory: () => {
    function resetSelectedBool(itemsArray) {
      if (!itemsArray || !Array.isArray(itemsArray)) {
        return; // Guard clause for invalid input
      }

      for (const item of itemsArray) {
        // Set selected_bool to false for the current item
        item.selected_bool = false;

        // If the item has a nested 'items' array, recurse
        if (item.items && Array.isArray(item.items) && item.items.length > 0) {
          resetSelectedBool(item.items);
        }
      }
    }

    let arrCategory = get().arrCategory;
    resetSelectedBool(arrCategory);
    // Semua Kategori
    arrCategory[0].selected_bool = true;

    set({
      arrCategory,
      selectedCategory: null,
    });
  },

  onFetchCategory: async (resolve) => {
    let response = await ApiHelper.get(
      "kiosk/kiosk/product/etalase/data/tree",
      {
        pagination_bool: false,
      }
    );
    if (response.status === 200) {
      set({ arrCategory: response.results.data });
    } else {
      get().onNotify(response.message, "error");
    }
    resolve();
  },
  onRefreshProduct: async (resolve) => {
    set({
      pageProduct: 1,
      limitProduct: 10,
      totalPageProduct: 1,
      arrProducts: [],
      totalProduct: 0,
      isGetProduct: true,
    });
    await get().onFetchProduct(resolve);
    set({ isGetProduct: false });
  },
  onFetchProduct: async (resolve) => {
    set({ totalProduct: 0 });
    let params = {
      // pagination_bool: false,
      limit: get().limitProduct,
      page: get().pageProduct,
      search: get().searchProduct,
      aol_id: get().selected_aol.aol_id,
      aol_session_database: get().selected_aol.database,
      branch_accurate_id: get().selected_branch.accurate_id,
    };
    if (get().selectedCategory) {
      params.etalase_id = get().selectedCategory.id;
    }
    if (get().sortProduct !== "placeholder") {
      params.sort = get().sortProduct;
    }
    params[`selling_price[gte]`] = get().filterProductSellingPriceRange[0];
    params[`selling_price[lte]`] = get().filterProductSellingPriceRange[1];

    let response = await ApiHelper.get("kiosk/kiosk/product/data", params);
    let arrProduct = get().arrProducts;
    let totalProduct = 0;
    let totalPageProduct = 1;
    if (response.status === 200) {
      arrProduct = [...arrProduct, ...response.results.data];
      totalProduct = response.results.total;
      totalPageProduct = response.results.pagination.total_page;
    } else {
      get().onNotify(response.message, "error");
    }
    set({
      arrProducts: arrProduct,
      totalProduct,
      totalPageProduct,
    });

    if (resolve) {
      resolve();
    }
  },
  onLoadMoreProduct: async (resolve) => {
    if (get().pageProduct >= get().totalPageProduct) {
      return;
    }
    set({ pageProduct: get().pageProduct + 1, isLoadMoreProduct: true });
    await get().onFetchProduct(resolve);
    set({ isLoadMoreProduct: false });
  },
  onFetchSummary: async (resolve) => {
    let params = {
      [`key[in]`]: "product_max_selling_price",
    };
    let response = await ApiHelper.get("kiosk/kiosk/summary/data", params);
    if (response.status === 200) {
      let filterProductSellingPriceMaxRange =
        get().filterProductSellingPriceMaxRange;
      let filterProductSellingPriceRange = get().filterProductSellingPriceRange;
      response.results.data.forEach((element) => {
        if (element.key === "product_max_selling_price") {
          filterProductSellingPriceRange[1] = Number(element.value_int);
          filterProductSellingPriceMaxRange = Number(element.value_int);
        }
      });

      set({
        filterProductSellingPriceRange,
        filterProductSellingPriceMaxRange,
      });
    } else {
      get().onNotify(response?.message || "Terjadi Kesalahan", "error");
    }
    if (resolve) {
      resolve();
    }
  },

  onSelectedCategoryListeners: ([...indices], item) => {
    // console.log("onSelectedCategoryListeners", indices);

    // Helper function to recursively clear all selected_bool flags
    const clearAllSelections = (items) => {
      items.forEach((item) => {
        item.selected_bool = false;
        if (item.items && item.items.length > 0) {
          clearAllSelections(item.items);
        }
      });
    };

    // Helper function to recursively navigate and set selection
    const setSelectionAtPath = (items, pathIndices, currentDepth = 0) => {
      if (currentDepth >= pathIndices.length) return;

      const currentIndex = pathIndices[currentDepth];
      if (currentIndex >= 0 && currentIndex < items.length) {
        const currentItem = items[currentIndex];

        // If this is the final index in the path, select this item
        if (currentDepth === pathIndices.length - 1) {
          currentItem.selected_bool = true;
        } else {
          // If this is not the final index, set it as selected and continue deeper
          currentItem.selected_bool = true;
          if (currentItem.items && currentItem.items.length > 0) {
            setSelectionAtPath(
              currentItem.items,
              pathIndices,
              currentDepth + 1
            );
          }
        }
      }
    };

    let updatedCategories = [...get().arrCategory];

    // Clear all selections first
    clearAllSelections(updatedCategories);

    // Set selection based on the provided path
    if (indices.length > 0) {
      setSelectionAtPath(updatedCategories, indices);
    }

    // console.log("Updated categories:", updatedCategories);
    set({ arrCategory: updatedCategories, selectedCategory: item });
    get().onRefreshProduct(null);
  },
  onSearchProductListeners: (value) => {
    set({ searchProduct: value });
    // 2. Clear any existing debounce timer
    if (get().searchProductDebounceTimer) {
      clearTimeout(get().searchProductDebounceTimer);
    }

    // 3. Set a new timer to call onFetchProduct
    // We only proceed to fetch if there's a value, or to clear results if it's empty
    let searchProductDebounceTimer = setTimeout(() => {
      get().onRefreshProduct(null);
    }, 500);

    set({ searchProductDebounceTimer });
  },
  onSortProductListeners: (value) => {
    set({ sortProduct: value });
    get().onRefreshProduct(null);
  },

  onFilterProductSellingPriceRangeListeners: (event, value) => {
    set({ filterProductSellingPriceRange: value });
  },
});

// modal
const modal = (set, get) => ({
  onShowCheckIn: () => {
    if (usePersistKioskPosStore.getState().arrCart.length <= 0) {
      get().onNotify("Silahkan pilih produk.", "warning");
      return;
    }

    useCheckInStore.getState().onShowDialog();
  },
});

export const useKioskPosStore = create((...a) => ({
  ...page(...a),
  ...modal(...a),
}));

const useTrackedKioskPosStore = createTrackedSelector(useKioskPosStore);

export default useTrackedKioskPosStore;
