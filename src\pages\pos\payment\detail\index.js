import { useEffect, useRef } from "react";
import { useRouter } from "next/router";
import useTrackedPaymentDetailStore, {
  usePaymentDetailStore,
} from "@/store/kiosk/pos/payment/detail/store";
import usePersistKioskPosStore from "@/store/kiosk/pos/storePersist";
import styles from "@/styles/Pos.module.css";
import AuthWrapper from "@/components/wrapper/AuthWrapper";
import Constants from "@/utils/Constants";
import Loading from "@/components/modal/Loading";
import MySnackbar from "@/components/MySnackbar";
import ModalConfirmation from "@/components/modal/ModalConfirmation";
import CommonHelper from "@/utils/CommonHelper";
import MomentHelper from "@/utils/MomentHelper";
import Router from "next/router";

const KioskPaymentDetailPage = ({
  isAuthLoading,
  isLoggedIn,
  user,
  selected_aol,
  selected_branch,
}) => {
  const router = useRouter();
  const { objCustomer } = usePersistKioskPosStore();

  const ref_Loading = useRef(null);
  const ref_MySnackbar = useRef(null);
  const ref_ModalConfirmation = useRef(null);

  useEffect(() => {
    usePaymentDetailStore.getState().setState({
      ref_Loading: ref_Loading,
      ref_MySnackbar: ref_MySnackbar,
      ref_ModalConfirmation: ref_ModalConfirmation,
    });

    // clean up
    return () => {
      if (usePaymentDetailStore.getState().intervalRefresh) {
        clearInterval(usePaymentDetailStore.getState().intervalRefresh);
      }
    };
  }, []);

  // Redirect if no customer selected
  useEffect(() => {
    if (!isAuthLoading && isLoggedIn) {
      if (!objCustomer?.id) {
        router.push("/pos");
      }

      usePaymentDetailStore.getState().onInit();
    }
  }, [isAuthLoading, isLoggedIn, objCustomer]);

  if (!objCustomer) return null; // Already redirecting

  return (
    <>
      <div className={styles.ct_containers}>
        <div className={styles.contents}>
          <RenderPaymentDetail />
        </div>
      </div>

      {/* Loading dialog */}
      <Loading ref={ref_Loading} />
      <MySnackbar ref={ref_MySnackbar} />
      <ModalConfirmation ref={ref_ModalConfirmation} />
    </>
  );
};

const RenderPaymentDetail = () => {
  const paymentDetail = usePaymentDetailStore((state) => state.paymentDetail);

  if (!paymentDetail) return null;

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "menunggu verifikasi":
        return { backgroundColor: "rgba(255, 193, 7, 0.1)", color: "#ffc107" };
      case "lunas":
      case "paid":
        return { backgroundColor: "rgba(40, 167, 69, 0.1)", color: "#28a745" };
      case "dibatalkan":
      case "cancelled":
        return { backgroundColor: "rgba(220, 53, 69, 0.1)", color: "#dc3545" };
      default:
        return {
          backgroundColor: "rgba(108, 117, 125, 0.1)",
          color: "#6c757d",
        };
    }
  };

  return (
    <div style={{ padding: "1rem 2rem", maxWidth: "800px", margin: "0 auto" }}>
      {/* Header Section */}
      <div
        style={{
          backgroundColor: "#fff",
          border: "1px solid #ececec",
          borderRadius: "8px",
          padding: "1.5rem",
          marginBottom: "1rem",
          boxShadow:
            "-8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(222 222 222 / 25%)",
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "flex-start",
            marginBottom: "1rem",
          }}
        >
          <div className="flex flex-row gap-4 items-center">
            {/* back button */}
            <a
              style={{
                height: "40px",
                width: "40px",
                borderRadius: "2px",
                WebkitBorderRadius: "2px",
                MozBorderRadius: "2px",
                MsBorderRadius: "2px",
                OBorderRadius: "2px",
                backgroundColor: "#2d3436",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                textDecoration: "none",
                cursor: "pointer",
              }}
              onClick={() => {
                Router.replace({
                  pathname: "/pos",
                  query: { from: "payment_detail" },
                });
              }}
            >
              <i
                className="ph ph-arrow-left"
                style={{ color: "#fff", fontSize: "1.4rem" }}
              ></i>
            </a>
            <div>
              <h2
                style={{
                  fontSize: "1.5rem",
                  fontWeight: "600",
                  color: "#2f3640",
                }}
              >
                Detail{" "}
                {paymentDetail.status === "Menunggu Verifikasi"
                  ? "Pesanan"
                  : "pembayaran"}
              </h2>
              <p style={{ margin: 0, color: "#808080", fontSize: "0.9rem" }}>
                Informasi transaksi
              </p>
            </div>
          </div>
          <div
            style={{
              ...getStatusColor(paymentDetail.status),
              padding: "0.5rem 1rem",
              borderRadius: "20px",
              fontSize: "0.85rem",
              fontWeight: "600",
              textAlign: "center",
              minWidth: "120px",
            }}
          >
            {paymentDetail.status}
          </div>
        </div>

        <div
          style={{
            display: "grid",
            gridTemplateColumns: "1fr 1fr",
            gap: "1rem",
          }}
        >
          <div>
            <label
              style={{
                fontSize: "0.8rem",
                color: "#808080",
                textTransform: "uppercase",
                fontWeight: "600",
              }}
            >
              Nomor Transaksi
            </label>
            <div
              style={{
                fontSize: "1rem",
                fontWeight: "600",
                color: "#2f3640",
                marginTop: "0.25rem",
              }}
            >
              {paymentDetail.number}
            </div>
          </div>
          <div>
            <label
              style={{
                fontSize: "0.8rem",
                color: "#808080",
                textTransform: "uppercase",
                fontWeight: "600",
              }}
            >
              Tanggal Transaksi
            </label>
            <div
              style={{
                fontSize: "1rem",
                fontWeight: "600",
                color: "#2f3640",
                marginTop: "0.25rem",
              }}
            >
              {MomentHelper.format(
                paymentDetail.input_datetime,
                MomentHelper.datetimeFormatReadable2
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Customer Information */}
      <div
        style={{
          backgroundColor: "#fff",
          border: "1px solid #ececec",
          borderRadius: "8px",
          padding: "1.5rem",
          marginBottom: "1rem",
          boxShadow:
            "-8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(222 222 222 / 25%)",
        }}
      >
        <h3
          style={{
            margin: "0 0 1rem 0",
            fontSize: "1.2rem",
            fontWeight: "600",
            color: "#2f3640",
          }}
        >
          Informasi Pelanggan
        </h3>
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "1fr 1fr",
            gap: "1rem",
          }}
        >
          <div>
            <label
              style={{
                fontSize: "0.8rem",
                color: "#808080",
                textTransform: "uppercase",
                fontWeight: "600",
              }}
            >
              Nama Pelanggan
            </label>
            <div
              style={{
                fontSize: "1rem",
                fontWeight: "600",
                color: "#2f3640",
                marginTop: "0.25rem",
              }}
            >
              {paymentDetail.customer_name}
            </div>
          </div>
          <div>
            <label
              style={{
                fontSize: "0.8rem",
                color: "#808080",
                textTransform: "uppercase",
                fontWeight: "600",
              }}
            >
              Kode Pelanggan
            </label>
            <div
              style={{
                fontSize: "1rem",
                fontWeight: "600",
                color: "#2f3640",
                marginTop: "0.25rem",
              }}
            >
              {paymentDetail.customer_code}
            </div>
          </div>
          <div>
            <label
              style={{
                fontSize: "0.8rem",
                color: "#808080",
                textTransform: "uppercase",
                fontWeight: "600",
              }}
            >
              Email
            </label>
            <div
              style={{
                fontSize: "1rem",
                color: "#2f3640",
                marginTop: "0.25rem",
              }}
            >
              {paymentDetail.customer_email || "-"}
            </div>
          </div>
          <div>
            <label
              style={{
                fontSize: "0.8rem",
                color: "#808080",
                textTransform: "uppercase",
                fontWeight: "600",
              }}
            >
              WhatsApp
            </label>
            <div
              style={{
                fontSize: "1rem",
                color: "#2f3640",
                marginTop: "0.25rem",
              }}
            >
              {paymentDetail.customer_whatsapp || "-"}
            </div>
          </div>
          <div>
            <label
              style={{
                fontSize: "0.8rem",
                color: "#808080",
                textTransform: "uppercase",
                fontWeight: "600",
              }}
            >
              Kategori Pelanggan
            </label>
            <div
              style={{
                fontSize: "1rem",
                color: "#2f3640",
                marginTop: "0.25rem",
              }}
            >
              {paymentDetail.customer_category_name}
            </div>
          </div>
        </div>
      </div>

      {/* Affiliate Information (if exists) */}
      {paymentDetail.affiliate_name && (
        <div
          style={{
            backgroundColor: "#fff",
            border: "1px solid #ececec",
            borderRadius: "8px",
            padding: "1.5rem",
            marginBottom: "1rem",
            boxShadow:
              "-8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(222 222 222 / 25%)",
          }}
        >
          <h3
            style={{
              margin: "0 0 1rem 0",
              fontSize: "1.2rem",
              fontWeight: "600",
              color: "#2f3640",
            }}
          >
            Informasi Afiliasi
          </h3>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr",
              gap: "1rem",
            }}
          >
            <div>
              <label
                style={{
                  fontSize: "0.8rem",
                  color: "#808080",
                  textTransform: "uppercase",
                  fontWeight: "600",
                }}
              >
                Nama Afiliasi
              </label>
              <div
                style={{
                  fontSize: "1rem",
                  fontWeight: "600",
                  color: "#2f3640",
                  marginTop: "0.25rem",
                }}
              >
                {paymentDetail.affiliate_name}
              </div>
            </div>
            <div>
              <label
                style={{
                  fontSize: "0.8rem",
                  color: "#808080",
                  textTransform: "uppercase",
                  fontWeight: "600",
                }}
              >
                Kode Afiliasi
              </label>
              <div
                style={{
                  fontSize: "1rem",
                  color: "#2f3640",
                  marginTop: "0.25rem",
                }}
              >
                {paymentDetail.affiliate_code || "-"}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Product Summary */}
      <RenderPaymentProduct />

      <RenderPaymentVoucher />

      {/* Financial Summary */}
      <RenderPaymentSummary />
    </div>
  );
};

const RenderPaymentProduct = () => {
  const paymentDetail = usePaymentDetailStore((state) => state.paymentDetail);

  if (!paymentDetail) return null;

  return (
    <div
      style={{
        backgroundColor: "#fff",
        border: "1px solid #ececec",
        borderRadius: "8px",
        padding: "1.5rem",
        marginBottom: "1rem",
        boxShadow:
          "-8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(222 222 222 / 25%)",
      }}
    >
      <h3
        style={{
          margin: "0 0 1rem 0",
          fontSize: "1.2rem",
          fontWeight: "600",
          color: "#2f3640",
        }}
      >
        Ringkasan Produk
      </h3>
      {paymentDetail.product_array.map((item, index) => {
        return <RenderPaymentProductItem key={index} item={item} />;
      })}
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "1fr 1fr",
          gap: "1rem",
        }}
      >
        <div>
          <label
            style={{
              fontSize: "0.8rem",
              color: "#808080",
              textTransform: "uppercase",
              fontWeight: "600",
            }}
          >
            Total Item
          </label>
          <div
            style={{
              fontSize: "1.2rem",
              fontWeight: "700",
              color: "#2f3640",
              marginTop: "0.25rem",
            }}
          >
            {paymentDetail.total_product_item} Item
          </div>
        </div>
        <div>
          <label
            style={{
              fontSize: "0.8rem",
              color: "#808080",
              textTransform: "uppercase",
              fontWeight: "600",
            }}
          >
            Subtotal
          </label>
          <div
            style={{
              fontSize: "1rem",
              fontWeight: "600",
              color: "#2f3640",
              marginTop: "0.25rem",
            }}
          >
            {CommonHelper.formatNumber(
              parseFloat(paymentDetail.total_product_selling_price),
              "idr"
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const RenderPaymentProductItem = ({ item }) => {
  return (
    <div
      style={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: "0.5rem",
      }}
    >
      <div
        style={{
          display: "flex",
          alignItems: "center",
        }}
      >
        <img
          alt={item.name}
          src={item.image_url || Constants.image_default.empty}
          style={{
            width: "40px",
            height: "40px",
            objectFit: "cover",
            marginRight: "0.5rem",
          }}
        />
        <div>
          <div
            style={{
              fontSize: "0.9rem",
              fontWeight: "600",
              color: "#2f3640",
              marginBottom: "0.25rem",
            }}
          >
            {item.name}
          </div>
          <div
            style={{
              fontSize: "0.8rem",
              color: "#808080",
            }}
          >
            {item.code}
          </div>
        </div>
      </div>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "flex-end",
        }}
      >
        <div
          style={{
            fontSize: "0.8rem",
            color: "#808080",
            marginBottom: "0.25rem",
          }}
        >
          {CommonHelper.formatNumber(parseFloat(item.quantity))}{" "}
          {item.selling_unit_name}
        </div>
        <div
          style={{
            fontSize: "0.9rem",
            fontWeight: "600",
            color: "#2f3640",
          }}
        >
          {CommonHelper.formatNumber(
            parseFloat(item.total_selling_price),
            "idr"
          )}
        </div>
      </div>
    </div>
  );
};

const RenderPaymentVoucher = () => {
  return null;

  const paymentDetail = usePaymentDetailStore((state) => state.paymentDetail);

  if (!paymentDetail) return null;

  if (!paymentDetail.voucher_name) return null;

  // TODO: multiple voucher?
  return (
    <div
      style={{
        backgroundColor: "#fff",
        border: "1px solid #ececec",
        borderRadius: "8px",
        padding: "1.5rem",
        marginBottom: "1rem",
        boxShadow:
          "-8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(222 222 222 / 25%)",
      }}
    >
      <h3
        style={{
          margin: "0 0 1rem 0",
          fontSize: "1.2rem",
          fontWeight: "600",
          color: "#2f3640",
        }}
      >
        Voucher
      </h3>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: "0.5rem",
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
          }}
        >
          <img
            alt={paymentDetail.voucher_name}
            src={
              paymentDetail.voucher_image_url || Constants.image_default.empty
            }
            style={{
              width: "40px",
              height: "40px",
              objectFit: "cover",
              marginRight: "0.5rem",
            }}
          />
          <div>
            <div
              style={{
                fontSize: "0.9rem",
                fontWeight: "600",
                color: "#2f3640",
                marginBottom: "0.25rem",
              }}
            >
              {paymentDetail.voucher_name}
            </div>
            <div
              style={{
                fontSize: "0.8rem",
                color: "#808080",
              }}
            >
              {paymentDetail.voucher_code}
            </div>
          </div>
        </div>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "flex-end",
          }}
        >
          <div
            style={{
              fontSize: "0.8rem",
              color: "#808080",
              marginBottom: "0.25rem",
            }}
          >
            1 Voucher
          </div>
          <div
            style={{
              fontSize: "0.9rem",
              fontWeight: "600",
              color: "#2f3640",
            }}
          >
            {CommonHelper.formatNumber(
              parseFloat(paymentDetail.voucher_nominal),
              "idr"
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const RenderPaymentSummary = () => {
  const paymentDetail = usePaymentDetailStore((state) => state.paymentDetail);

  if (!paymentDetail) return null;

  if (paymentDetail.status === "Menunggu Verifikasi") return null;

  return (
    <div
      style={{
        backgroundColor: "#fff",
        border: "1px solid #ececec",
        borderRadius: "8px",
        padding: "1.5rem",
        marginBottom: "1rem",
        boxShadow:
          "-8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(222 222 222 / 25%)",
      }}
    >
      <h3
        style={{
          margin: "0 0 1rem 0",
          fontSize: "1.2rem",
          fontWeight: "600",
          color: "#2f3640",
        }}
      >
        Ringkasan Pembayaran
      </h3>

      {/* Subtotal and Discounts */}
      <div
        style={{
          borderBottom: "1px solid #f0f0f0",
          paddingBottom: "1rem",
          marginBottom: "1rem",
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "0.5rem",
          }}
        >
          <span style={{ fontSize: "1rem", color: "#2f3640" }}>Subtotal</span>
          <span
            style={{ fontSize: "1rem", fontWeight: "600", color: "#2f3640" }}
          >
            {CommonHelper.formatNumber(
              parseFloat(paymentDetail.total_product_selling_price),
              "idr"
            )}
          </span>
        </div>

        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "0.5rem",
          }}
        >
          <span style={{ fontSize: "1rem", color: "#e74c3c" }}>
            Diskon Transaksi ({paymentDetail.total_discount_percent}%)
          </span>
          <span
            style={{
              fontSize: "1rem",
              fontWeight: "600",
              color: "#e74c3c",
            }}
          >
            -
            {CommonHelper.formatNumber(
              parseFloat(paymentDetail.total_discount_nominal),
              "idr"
            )}
          </span>
        </div>
      </div>

      {/* Additional Charges */}
      <div
        style={{
          borderBottom: "1px solid #f0f0f0",
          paddingBottom: "1rem",
          marginBottom: "1rem",
        }}
      >
        {parseFloat(paymentDetail.charge_nominal) > 0 && (
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "0.5rem",
            }}
          >
            <span style={{ fontSize: "1rem", color: "#f39c12" }}>
              Biaya Layanan ({paymentDetail.charge_percent}%)
            </span>
            <span
              style={{
                fontSize: "1rem",
                fontWeight: "600",
                color: "#f39c12",
              }}
            >
              {CommonHelper.formatNumber(
                parseFloat(paymentDetail.charge_nominal),
                "idr"
              )}
            </span>
          </div>
        )}

        {parseFloat(paymentDetail.tax_nominal) > 0 && (
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "0.5rem",
            }}
          >
            <span style={{ fontSize: "1rem", color: "#f39c12" }}>
              Pajak ({paymentDetail.tax_percent}%)
            </span>
            <span
              style={{
                fontSize: "1rem",
                fontWeight: "600",
                color: "#f39c12",
              }}
            >
              {CommonHelper.formatNumber(
                parseFloat(paymentDetail.tax_nominal),
                "idr"
              )}
            </span>
          </div>
        )}

        {paymentDetail.other_cost_label &&
          parseFloat(paymentDetail.other_cost_nominal) > 0 && (
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: "0.5rem",
              }}
            >
              <span style={{ fontSize: "1rem", color: "#f39c12" }}>
                {paymentDetail.other_cost_label}
              </span>
              <span
                style={{
                  fontSize: "1rem",
                  fontWeight: "600",
                  color: "#f39c12",
                }}
              >
                {CommonHelper.formatNumber(
                  parseFloat(paymentDetail.other_cost_nominal),
                  "idr"
                )}
              </span>
            </div>
          )}

        {parseFloat(paymentDetail.added_round_nominal_grand_total) !== 0 && (
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "0.5rem",
            }}
          >
            <span style={{ fontSize: "1rem", color: "#6c757d" }}>
              Pembulatan
            </span>
            <span
              style={{
                fontSize: "1rem",
                fontWeight: "600",
                color: "#6c757d",
              }}
            >
              {parseFloat(paymentDetail.added_round_nominal_grand_total) >= 0
                ? "+"
                : ""}
              {CommonHelper.formatNumber(
                parseFloat(paymentDetail.added_round_nominal_grand_total),
                "idr"
              )}
            </span>
          </div>
        )}
      </div>

      {/* Grand Total */}
      <div
        style={{
          backgroundColor: "#f8f9fa",
          padding: "1rem",
          borderRadius: "6px",
          marginBottom: "1rem",
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "0.5rem",
          }}
        >
          <span
            style={{
              fontSize: "1.2rem",
              fontWeight: "700",
              color: "#2f3640",
            }}
          >
            Total Pembayaran
          </span>
          <span
            style={{
              fontSize: "1.5rem",
              fontWeight: "700",
              color: "var(--base-color)",
            }}
          >
            {CommonHelper.formatNumber(
              parseFloat(paymentDetail.grand_total),
              "idr"
            )}
          </span>
        </div>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <span style={{ fontSize: "1rem", color: "#6c757d" }}>
            Jumlah Dibayar
          </span>
          <span
            style={{
              fontSize: "1.2rem",
              fontWeight: "600",
              color: "#28a745",
            }}
          >
            {CommonHelper.formatNumber(
              parseFloat(paymentDetail.paid_nominal),
              "idr"
            )}
          </span>
        </div>
      </div>
    </div>
  );
};

export default AuthWrapper(KioskPaymentDetailPage, {
  redirectTo: Constants.webUrl.login,
});
