/*
Created by esoda
Created on Feb, 2025
Contact esoda.id
*/

import React, { useEffect, useRef } from "react";
import Tooltip from "@mui/material/Tooltip";
import CommonHelper from "@/utils/CommonHelper";
import Constants from "@/utils/Constants";
import styles from "@/styles/Kiosk.module.css";
import TimePicker from "@/components/TimePicker";
import Loading from "@/components/modal/Loading";
import MySnackbar from "@/components/MySnackbar";
import CustumerCheckin from "@/components/pages/kiosk/pos/kioskCheckin";
import Router from "next/router";
import useTrackedKioskPosStore, {
  useKioskPosStore,
} from "@/store/kiosk/pos/store";
import useTrackedPersistKioskPosStore, {
  usePersistKioskPosStore,
} from "@/store/kiosk/pos/storePersist";
import AuthWrapper from "@/components/wrapper/AuthWrapper";
import { Skeleton, Slider } from "@mui/material";
import Image from "next/image";
import Input from "@/components/libs/Input";

const KioskPosPage = ({
  isAuthLoading,
  isLoggedIn,
  selected_aol,
  selected_branch,
}) => {
  const {
    isReady,
    setState,
    onLogoutListeners,
  } = useTrackedKioskPosStore();

  const ref_Loading = useRef(null);
  const ref_MySnackbar = useRef(null);

  useEffect(() => {
    setState({ ref_Loading, ref_MySnackbar });
  }, []);

  useEffect(() => {
    if (!isAuthLoading && isLoggedIn) {
      setState(
        {
          selected_aol,
          selected_branch,
        },
        () => {
          useKioskPosStore.getState().onInit();
        }
      );
    }
  }, [isAuthLoading, isLoggedIn]);

  const onInitializeListeners = () => {
    setState({ isReady: true });
  };

  return (
    <>
      {!isReady && (
        <div className={styles.loading}>
          <img
            className={styles.overlay}
            src="https://media.screenpal.com/blog/wp-content/uploads/2020/01/13070818/animated-movies.gif"
          />
          <div className={styles.top}>
            <img src="https://els.id/wp-content/uploads/2023/08/ELS-ID-oren.png" />
          </div>
          <div className={styles.center}>
            <h3>Kiosk</h3>
            <h2>Self Service Station</h2>
            <p>
              Maximize your workday with our professional kiosk self service
              station. We believe that the word creative has a positive effect
              on the world. With creativity, a lot of goodness can be created
              for our business.
            </p>

            {/* isAuthLoading & !isLoggedIn */}
            {isAuthLoading && !isLoggedIn && (
              <button className="button" disabled>
                <i className="ph ph-bold ph-shopping-cart-simple"></i>
                <span>memuat data...</span>
              </button>
            )}

            {/* !isAuthLoading & isLoggedIn */}
            {!isAuthLoading && isLoggedIn && (
              <button
                className="button"
                onClick={() => onInitializeListeners()}
              >
                <i className="ph ph-bold ph-shopping-cart-simple"></i>
                <span>MULAI BELANJA</span>
              </button>
            )}
          </div>
          <div className={styles.bottom}>
            <h4>
              {/* Copyright &#169; 2025. Era Solusi Data. All Rights Reserved. */}
            </h4>
          </div>
        </div>
      )}
      {isReady && (
        <div className={styles.kiosk}>
          <div className={styles.categories}>
            <div className={styles.logo}>
              <img src="https://els.id/wp-content/uploads/2023/08/ELS-ID-oren.png" />
            </div>
            <RenderFilterSellingPrice />
            <RenderCategories />
          </div>
          <div className={styles.contents}>
            <div className={styles.topbar}>
              <div className={styles.store}>
                <div className={styles.info}>
                  <div className={styles.name}>{selected_branch?.name}</div>
                  {selected_branch?.address !== undefined &&
                    selected_branch?.address !== null &&
                    selected_branch?.address !== "" && (
                      <div className={styles.address}>
                        {selected_branch?.address}
                      </div>
                    )}
                </div>
                <div className={styles.date}>
                  <div className={styles.cashier}>Selamat Datang</div>
                  <TimePicker />
                </div>
                <div>
                  <button
                    className="button outlined ml-4 danger"
                    onClick={onLogoutListeners}
                  >
                    <i className="ph ph-bold ph-power mr-0"></i>
                  </button>
                </div>
              </div>
              <RenderProductSearchBar />
              <RenderTotalProduct />
            </div>
            <RenderProducts />
            <RenderFooter />
          </div>
        </div>
      )}

      {/* Loading dialog */}
      <Loading ref={ref_Loading} />
      <MySnackbar ref={ref_MySnackbar} />

      {/* Customer Checkin dialog */}
      <CustumerCheckin />
    </>
  );
};

const RenderFilterSellingPrice = () => {
  const {
    filterProductSellingPriceRange,
    filterProductSellingPriceMaxRange,
    onFilterProductSellingPriceRangeListeners,
    onRefreshProduct,
  } = useTrackedKioskPosStore();
  // const filterProductSellingPriceRange = useKioskPosStore(
  //   (state) => state.filterProductSellingPriceRange
  // );

  function valuetext(value) {
    return `${value}°C`;
  }
  return (
    <div className={`mt-4 input-form p-4`}>
      <div className={`label`}>Filter Harga</div>
      <div className="" style={{ padding: "0px 1.5rem" }}>
        <Slider
          getAriaLabel={() => "Temperature range"}
          value={filterProductSellingPriceRange}
          onChange={onFilterProductSellingPriceRangeListeners}
          valueLabelDisplay="auto"
          valueLabelFormat={(value) => {
            return CommonHelper.formatNumberSuffix(value, {
              decimalPlaces: 2,
              useShortForm: true,
            });
          }}
          getAriaValueText={valuetext}
          min={0}
          max={filterProductSellingPriceMaxRange}
        />
      </div>
      <div className="flex justify-end">
        <button className="button" onClick={() => onRefreshProduct(null)}>
          <i className="ph ph-bold ph-check-circle"></i>
          <span>Terapkan</span>
        </button>
      </div>
    </div>
  );
};

// Optimized category item component with React.memo to prevent unnecessary rerenders
// Supports unlimited nesting levels
const CategoryItem = React.memo(
  ({ item, index, level = 0, indexPath = [], onSelectedCategoryListeners }) => {
    // Current path including this item's index
    const currentPath = [...indexPath, index];

    // Determine CSS class based on nesting level
    const getCategoryClass = () => {
      switch (level) {
        case 0:
          return styles.item;
        case 1:
          return styles.subs;
        case 2:
          return styles.menus;
        default:
          return styles.menus; // All deeper levels use menus style
      }
    };

    // Calculate dynamic margin for deeper nesting levels
    const getDynamicStyle = () => {
      if (level <= 2) return {}; // Use default CSS for levels 0, 1, 2

      // For levels 3+, calculate margin dynamically
      // Base margin: 37px (from .menus), then add 19px for each additional level
      const baseMargin = 37;
      const additionalMargin = (level - 2) * 19;
      return {
        marginLeft: `${baseMargin + additionalMargin}px`,
      };
    };

    // Handle click - pass the complete path to the store
    const handleClick = () => {
      onSelectedCategoryListeners([...currentPath], item);
    };

    return (
      <>
        <div
          className={`${getCategoryClass()} ${
            item.selected_bool ? styles.active : ""
          }`}
          style={getDynamicStyle()}
          onClick={() => handleClick(item)}
        >
          <div className={styles.title}>
            <div className={styles.name}>
              <div>{item.name}</div>
              <div>
                {item.counts > 0
                  ? `${CommonHelper.formatNumber(item.counts)} Produk`
                  : "Tidak Ada Produk"}
              </div>
            </div>
            {item.items && item.items.length > 0 && (
              <i
                className={`ph ph-bold ${
                  item.selected_bool ? "ph-caret-up" : "ph-caret-down"
                }`}
              />
            )}
          </div>
        </div>

        {/* Render child categories when parent is selected - supports unlimited nesting */}
        {item.selected_bool &&
          item.items &&
          item.items.length > 0 &&
          item.items.map((childItem, childIndex) => (
            <CategoryItem
              key={`${currentPath.join("-")}-${childIndex}`}
              item={childItem}
              index={childIndex}
              level={level + 1}
              indexPath={currentPath}
              onSelectedCategoryListeners={onSelectedCategoryListeners}
            />
          ))}
      </>
    );
  }
);

// Set display name for better debugging
CategoryItem.displayName = "CategoryItem";

// Optimized categories container with proper Zustand selector
const RenderCategories = () => {
  // Use specific selector to only rerender when arrCategory changes
  const arrCategory = useKioskPosStore((state) => state.arrCategory);

  // Get the callback function separately to avoid unnecessary rerenders
  const { onSelectedCategoryListeners } = useTrackedKioskPosStore();

  // Early return if no categories
  if (!arrCategory || arrCategory.length === 0) {
    return null;
  }  

  return (
    <>
      {arrCategory.map((item, index) => (
        <CategoryItem
          key={item.id || index} // Use item.id if available, fallback to index
          item={item}
          index={index}
          level={0}
          indexPath={[]} // Start with empty path for root level
          onSelectedCategoryListeners={onSelectedCategoryListeners}
        />
      ))}
    </>
  );
};

// Set display name for better debugging
RenderCategories.displayName = "RenderCategories";

const RenderTotalProduct = React.memo(() => {
  const totalProduct = useKioskPosStore((state) => state.totalProduct);

  if (totalProduct === 0) {
    return null;
  }

  return (
    <div className={styles.search_results}>
      {CommonHelper.formatNumber(totalProduct)} <span>Produk ditemukan</span>
    </div>
  );
});

const RenderProducts = () => {
  const arrProducts = useKioskPosStore((state) => state.arrProducts);
  const pageProduct = useKioskPosStore((state) => state.pageProduct);
  const totalPageProduct = useKioskPosStore((state) => state.totalPageProduct);
  const totalProduct = useKioskPosStore((state) => state.totalProduct);
  const isGetProduct = useKioskPosStore((state) => state.isGetProduct);
  const isLoadMoreProduct = useKioskPosStore(
    (state) => state.isLoadMoreProduct
  );
  const onLoadMoreProduct = useKioskPosStore(
    (state) => state.onLoadMoreProduct
  );

  if (isGetProduct) {
    return (
      <div className={styles.products} data-length={10}>
        {Array.from({ length: 10 }).map((item, index) => (
          <div key={index} className={styles.item}>
            <div>
              <Skeleton
                variant="rectangular"
                height={200}
                sx={{ borderRadius: "2px" }}
              />
            </div>
            <div className="my-4">
              <Skeleton variant="text" width={100} height={14} />
              <Skeleton variant="text" width={100} height={16} />
              <Skeleton variant="text" width={100} height={16} />
            </div>
            <div>
              <Skeleton variant="rounded" height={40} />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!arrProducts || arrProducts.length === 0) {
    return (
      <div
        className={`${styles.empty} flex flex-col justify-center items-center gap-4 flex-grow`}
      >
        <Image
          src="/assets/images/img_empty.png"
          alt="Tidak Ada Data"
          width={150}
          height={150}
        />
        <h3>Tidak Ada Data</h3>
        <p>
          Tidak ada produk yang tersedia. Atau data yang Anda cari tidak
          ditemukan.
        </p>
      </div>
    );
  }

  return (
    <>
      <div className={styles.products} data-length={arrProducts.length}>
        {arrProducts.map((item, index) => (
          <RenderProductItem key={item.id || index} item={item} />
        ))}
        {isLoadMoreProduct && (
          <>
            {Array.from({ length: 10 }).map((item, index) => (
              <div key={index} className={styles.item}>
                <div>
                  <Skeleton
                    variant="rectangular"
                    height={200}
                    sx={{ borderRadius: "2px" }}
                  />
                </div>
                <div className="my-4">
                  <Skeleton variant="text" width={100} height={14} />
                  <Skeleton variant="text" width={100} height={16} />
                  <Skeleton variant="text" width={100} height={16} />
                </div>
                <div>
                  <Skeleton variant="rounded" height={40} />
                </div>
              </div>
            ))}
          </>
        )}
      </div>

      {pageProduct < totalPageProduct && (
        <div className={`flex flex-col justify-center items-center my-4`}>
          <button
            className="button"
            onClick={() => {
              onLoadMoreProduct(null);
            }}
          >
            <i className="ph ph-bold ph-arrow-down"></i>
            <span>Muat Lebih Banyak</span>
          </button>
        </div>
      )}
    </>
  );
};

const RenderProductItem = ({ item }) => {
  const onAddToCart = usePersistKioskPosStore((state) => state.onAddToCart);
  const isInCart = usePersistKioskPosStore(
    (state) => item.id in state.refCartProductIds
  );

  return (
    <div
      className={`${styles.item} ${isInCart ? styles.active : ""}`}
      onClick={() => onAddToCart(item)}
    >
      <img
        alt={item.name}
        src={item.image_url || Constants.image_default.empty}
      />
      <div
        className={styles.tags}
        style={{ fontSize: "0.8rem", color: "#2d3436" }}
      >
        {item.etalase_label}
      </div>
      <div className={styles.title}>{item.name}</div>
      <div className={styles.price}>
        <div>{CommonHelper.formatNumber(item.selling_price, "idr")}</div>
        <div>
          {CommonHelper.formatNumberSuffix(item.stock_balance, {
            decimalPlaces: 0,
            useShortForm: true,
          })}{" "}
          {item.unit}
        </div>
      </div>
      <button>
        <i className="ph ph-bold ph-shopping-cart-simple mr-2"></i>
        Tambahkan
      </button>
    </div>
  );
};

const RenderProductSearchBar = () => {
  const {
    searchProduct,
    onSearchProductListeners,
    sortProduct,
    onSortProductListeners,
  } = useTrackedKioskPosStore();

  return (
    <div className={styles.searchbar}>
      <div className={styles.input_search}>
        <label htmlFor="search-input">
          <i className="ph ph-bold ph-magnifying-glass"></i>
        </label>
        <input
          type="text"
          name="search"
          id="search-input"
          value={searchProduct}
          onChange={(event) => {
            onSearchProductListeners(event.target.value);
          }}
          placeholder="Tuliskan produk yang ingin Anda cari..."
        />
      </div>
      <div>
        {/* select sort Input */}
        <Input
          label=""
          value={sortProduct}
          defaultValue={sortProduct}
          onChange={(event) => {
            onSortProductListeners(event.target.value);
          }}
          inputType="select"
          disablePlaceholder
          options={[
            { value: "placeholder", label: "Urutan Default" },
            // { value: "-bought_count", label: "Urutkan Produk Populer" },
            { value: "-created_datetime", label: "Urutkan Produk Terbaru" },
            { value: "-selling_price", label: "Urutkan Harga Tertinggi" },
            { value: "selling_price", label: "Urutkan Harga Terendah" },
            // stock
            { value: "-stock_balance", label: "Urutkan Stok Tertinggi" },
            { value: "stock_balance", label: "Urutkan Stok Terendah" },
          ]}
          styleInputs={{ height: "50px", marginTop: "0px" }}
        />
      </div>
      {/* <Tooltip title="Filter Data">
        <a
          style={{ backgroundColor: "#e67e22" }}
          onClick={() => {
            alert("Open Modal Filter Data");
          }}
        >
          <i className="ph ph-bold ph-funnel"></i>
        </a>
      </Tooltip> */}
    </div>
  );
};

const RenderFooter = () => {
  const { onShowCheckIn } = useTrackedKioskPosStore();
  const { arrCart, totalProductSellingPrice, onClearCart } =
    useTrackedPersistKioskPosStore();

  // if (arrCart.length === 0) {
  //   return null;
  // }

  return (
    <div className={`${styles.cart} flex-wrap`}>
      <div className={styles.left}>
        <div className={styles.qty}>
          <b>{CommonHelper.formatNumber(arrCart.length)}</b> Item Produk
        </div>
        <div className={styles.price}>
          {CommonHelper.formatNumber(totalProductSellingPrice, "idr")}
        </div>
      </div>
      <div className={styles.right}>
        <button
          className="button"
          onClick={() => {
            onShowCheckIn();
          }}
        >
          <i className="ph ph-bold ph-check-circle"></i>
          <span>Proses Transaksi</span>
        </button>
        <Tooltip title="Batalkan Transaksi (HAPUS)">
          <button
            className="button"
            style={{ backgroundColor: "#e74c3c" }}
            onClick={onClearCart}
          >
            <i className="ph ph-bold ph-x-circle"></i>
          </button>
        </Tooltip>
      </div>
    </div>
  );
};

export default AuthWrapper(KioskPosPage, {
  redirectTo: Constants.webUrl.login,
});
