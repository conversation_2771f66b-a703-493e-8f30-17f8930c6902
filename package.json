{"name": "els-kiosk", "version": "0.1.0", "private": true, "scripts": {"api": "cd api/ && php -S localhost:8078", "devnew": "next dev --turbopack", "dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^7.1.0", "@react-input/mask": "^2.0.4", "@react-input/number-format": "^2.0.3", "exceljs": "^4.4.0", "moment": "^2.30.1", "next": "15.3.2", "react": "^19.1.0", "react-dom": "^19.0.0", "react-tracked": "^2.0.1", "scheduler": "^0.26.0", "swr": "^2.3.3", "zustand": "^4.5.7"}}