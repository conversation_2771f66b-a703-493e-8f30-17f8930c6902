import React from "react";
import { Modal, Box, Alert } from "@mui/material";
import Loading from "@/components/modal/Loading";
import ApiHelper from "@/utils/ApiHelper";
import MySnackbar from "@/components/MySnackbar";
import Image from "next/image";
import InputAutoComplete2 from "@/components/libs/InputAutoComplete2";
import Constants from "@/utils/Constants";
import CommonHelper from "@/utils/CommonHelper";

export default class TransactionModalVerify extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDialog: false,
      formType: "verify",
      formData: null,
      formIndex: -1,

      detail: null,
    };
  }

  onFetchDetail = async () => {
    this.ref_Loading.onShowDialog();
    let response = await ApiHelper.get("kiosk/admin/transaction/detail", {
      id: this.state.formData.id,
    });

    let detail = null;
    if (response.status === 200) {
      detail = response.results.data;
    } else {
      this.onNotify(response.message, "error");
      this.onCloseDialog();
    }
    this.ref_Loading.onCloseDialog();
    this.setState({ detail });
  };

  onNotify = (message, severity = "info") => {
    this.ref_MySnackbar.onNotify(message, severity);
  };

  onLoading = (show, timout = 300) => {
    if (show) {
      this.ref_Loading.onShowDialog();
      return;
    } else {
      setTimeout(() => {
        this.ref_Loading.onCloseDialog();
      }, timout);
    }
  };

  onShowDialog = (formType, formData = null, formIndex = -1) => {
    this.setState(
      {
        formType,
        formData,
        formIndex,
        showDialog: true,
      },
      () => {
        this.onFetchDetail();
      }
    );
  };

  onCloseDialog = () => {
    this.setState({
      showDialog: false,
      formType: "verify",
      formData: null,
      formIndex: -1,

      detail: null,
    });
  };

  onValidateListeners = () => {
    if (this.state.formType === "verify") {
      this.actOnVerifyListeners();
    }
  };

  actOnVerifyListeners = async () => {
    this.ref_Loading.onShowDialog();

    let params = structuredClone(this.state.detail);
    const response = await ApiHelper.post(
      "kiosk/admin/transaction/verify",
      params
    );

    if (response.status === 200) {
      const { onResults } = this.props;
      if (onResults) {
        onResults(
          this.state.formType,
          this.state.formData,
          this.state.formIndex
        );
      }
      this.onCloseDialog();

      this.onNotify(response?.message || "Berhasil Verifikasi", "success");
    } else {
      this.onNotify(response?.message || "Terjadi Kesalahan", "error");
    }

    this.ref_Loading.onCloseDialog();
  };

  render() {
    return (
      <>
        <Modal open={this.state.showDialog} onClose={this.onCloseDialog}>
          <Box
            sx={{
              flex: 1,
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              display: "flex",
              width: "100wh",
              height: "100vh",
            }}
          >
            <div className="modal-content">
              <div className="modal-header">{this.renderHeader()}</div>
              <div className="modal-body" style={{ height: "fit-content" }}>
                {this.renderBody()}
              </div>
              <div className="modal-footer">{this.renderFooter()}</div>
            </div>
          </Box>
        </Modal>

        {/* Loading dialog */}
        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />
      </>
    );
  }

  renderHeader() {
    return (
      <>
        <div className="title">Verifikasi Transaksi</div>
        <span className="close" onClick={() => this.onCloseDialog()}>
          &times;
        </span>
      </>
    );
  }

  renderBody() {
    return <>{this.renderTransactionDetail()}</>;
  }

  renderTransactionDetail() {
    let objData = this.state.detail;

    if (!objData) {
      return (
        <div className="flex-rows no-right">
          <div className="row no-border">
            <div className="input-form">
              <Alert severity="info">Memuat detail transaksi...</Alert>
            </div>
          </div>
        </div>
      );
    }

    return (
      <>
        <div className="flex-rows no-right">
          {/* Transaction Information */}
          <div className="row wd100 no-border">
            <div className="box">
              <div className="title flex flex-row gap-4 flex-wrap">
                <div>Informasi Transaksi</div>
                <div className="content">
                  <span
                    className={`status ${
                      objData.status === "verified" ? "" : "unavailable"
                    }`}
                  >
                    {objData.status === "verified"
                      ? "Terverifikasi"
                      : "Belum Verifikasi"}
                  </span>
                </div>
              </div>
              <div className="detail_wrapper_grid">
                <div className="detail_container_grid">
                  <em>Nomor Transaksi</em>
                  <div className="content text">{objData.number || "-"}</div>
                </div>
                <div className="detail_container_grid">
                  <em>Tanggal Input</em>
                  <div className="content text">
                    {objData.input_datetime || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Nama Pelanggan</em>
                  <div className="content text">
                    {objData.customer_name || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>WhatsApp</em>
                  <div className="content text">
                    {objData.customer_whatsapp || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Afiliasi</em>
                  <div className="content text">
                    {objData.affiliate_name || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Kode Afiliasi</em>
                  <div className="content text">
                    {objData.affiliate_code || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Total Item</em>
                  <div className="content text">
                    {objData.total_product_item || "0"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Grand Total</em>
                  <div className="content text">
                    {new Intl.NumberFormat("id-ID", {
                      style: "currency",
                      currency: "IDR",
                    }).format(objData.grand_total || 0)}
                  </div>
                </div>
              </div>
            </div>

            {/* Product Details */}
            {objData.product_array && objData.product_array.length > 0 && (
              <div className="flex-rows no-right mt-5">
                <div className="row no-border">
                  <div className="box">
                    <div className="title">Detail Produk</div>
                    <div className="input-form">
                      {objData.product_array.map((product, index) => (
                        <div
                          key={index}
                          className="input-form"
                          style={{ marginTop: "1rem" }}
                        >
                          <div
                            style={{
                              backgroundColor: "#f8f9fa",
                              padding: "1rem",
                              borderRadius: "6px",
                              border: "1px solid #e9ecef",
                              display: "flex",
                              gap: "1rem",
                            }}
                          >
                            {/* Product Image */}
                            <div
                              style={{
                                flexShrink: 0,
                                width: "100px",
                                height: "100px",
                                borderRadius: "8px",
                                overflow: "hidden",
                                backgroundColor: "#ffffff",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                border: "1px solid #e9ecef",
                              }}
                            >
                              <Image
                                src={
                                  product.image_url ||
                                  Constants.image_default.empty
                                }
                                alt={product.name}
                                width={100}
                                height={100}
                                style={{ objectFit: "contain" }}
                              />
                            </div>

                            {/* Product Details */}
                            <div style={{ flex: 1 }}>
                              <div
                                style={{
                                  fontSize: "1.1rem",
                                  fontWeight: "600",
                                  color: "#2d3436",
                                  marginBottom: "0.5rem",
                                }}
                              >
                                {product.name}
                              </div>
                              <div
                                style={{
                                  display: "grid",
                                  gridTemplateColumns: "1fr 1fr",
                                  gap: "0.5rem",
                                }}
                              >
                                <div>
                                  <strong>Kode:</strong> {product.code}
                                </div>
                                <div>
                                  <strong>Kategori:</strong>{" "}
                                  {product.category_name}
                                </div>
                                <div>
                                  <strong>Harga:</strong>{" "}
                                  {Number(product.selling_price) < 0 ? "-" : ""}
                                  Rp
                                  {CommonHelper.formatNumberDecimal(
                                    Math.abs(product.selling_price)
                                  )}
                                </div>
                                <div>
                                  <strong>Jumlah:</strong>{" "}
                                  {parseFloat(product.quantity)}{" "}
                                  {product.unit_name}
                                </div>
                                <div style={{ gridColumn: "1 / -1" }}>
                                  <strong>Total:</strong>{" "}
                                  <span
                                    style={{
                                      color: "var(--base-color)",
                                      fontWeight: "600",
                                    }}
                                  >
                                    {Number(product.total_selling_price) < 0
                                      ? "-"
                                      : ""}
                                    Rp
                                    {CommonHelper.formatNumberDecimal(
                                      Math.abs(product.total_selling_price)
                                    )}
                                  </span>
                                </div>
                              </div>

                              {/* Serial Number Input */}
                              {product.type === "Persediaan" &&
                                product.serial_number_type === "UNIQUE" && (
                                  <div
                                    style={{
                                      marginTop: "1rem",
                                      paddingTop: "1rem",
                                      borderTop: "1px solid #e9ecef",
                                    }}
                                  >
                                    <InputAutoComplete2
                                      label="Serial Number"
                                      inputName={`serial_number_${product.id}`}
                                      formClass="full-width"
                                      dataUrl="kiosk/admin/product/serial/data"
                                      dataParams={{
                                        product_id: product.id,
                                      }}
                                      displayTitle="serial_number"
                                      displaySubtitle="warehouse_name"
                                      required={true}
                                      onChange={(item) => {
                                        if (item) {
                                          const updatedProducts = [
                                            ...objData.product_array,
                                          ];
                                          updatedProducts[index] = {
                                            ...product,
                                            serial_number: item.serial_number,
                                          };
                                          this.setState({
                                            detail: {
                                              ...objData,
                                              product_array: updatedProducts,
                                            },
                                          });
                                        }
                                      }}
                                    />
                                  </div>
                                )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </>
    );
  }
  renderFooter() {
    const { detail } = this.state;
    const isVerified = detail?.status === "verified";

    return (
      <>
        {!isVerified && (
          <button
            className="button info"
            onClick={() => this.onValidateListeners()}
          >
            <i className="ph ph-bold ph-check-circle"></i>
            <span>Verifikasi</span>
          </button>
        )}
        <button
          className="button cancel ml-0"
          onClick={() => this.onCloseDialog()}
        >
          <i className="ph ph-bold ph-x-circle"></i>
          <span>Tutup</span>
        </button>
      </>
    );
  }
}
